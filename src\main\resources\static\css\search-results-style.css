/* Search Results Page Styles - 强制防夜间模式 */

/* 搜索结果页面防夜间模式设置 */
:root {
    --contact-margin-top: 0rem;
    --contact-margin-bottom: 1rem;
    --contact-height: 168px;
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

html, body {
    height: 100%;
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

main.container {
    flex: 1;
    margin-bottom: auto;
}

footer {
    margin-top: auto;
    flex-shrink: 0;
}

.search-highlight {
    background-color: #fff3cd;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
}

.spqk01 {
    margin: 2px !important;
}

/* 搜索容器布局优化 */
.search-container {
    gap: 0.5rem;
    flex-wrap: nowrap;
}

.search-container .search-form {
    flex: 1;
    min-width: 0;
}

.search-container .btn {
    flex-shrink: 0;
    white-space: nowrap;
}

/* 确保清除搜索按钮可见 */
.search-container .ms-1 {
    margin-left: 0.5rem !important;
    z-index: 10;
}


.btn.btn-outline-light.btn-sm.ms-1{
    margin: 0!important;
}

/* ========== 搜索结果页面最终防夜间模式保护 ========== */
@media screen {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}

@media screen and (max-device-width: 768px) {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}
