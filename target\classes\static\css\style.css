/**
 * 全局样式文件 - 强制防夜间模式
 */

/* ========== 全局防夜间模式设置 ========== */
/* 强制所有页面元素使用亮色主题，不受系统夜间模式影响 */
:root {
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

html {
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
    background-color: #ffffff !important;
}

/* 强制所有元素防夜间模式 */
*, *::before, *::after {
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

/* 全局样式 - 强制防夜间模式 */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

/* 确保容器元素保持白色背景 */
.container, .container-fluid, .row, .col, [class*="col-"] {
    background-color: transparent !important;
    color: inherit !important;
}
    /* color: #333;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
} */

/* 搜索容器样式 */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-form {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    animation: slideIn 0.3s ease-out;
}

.search-form.active {
    display: flex !important;
}

.search-form.show {
    display: flex !important;
}



/* 搜索输入框样式 */
.search-input-mobile {
    width: 0;
    opacity: 0;
    padding: 0;
    margin: 0;
    font-size: 0.875rem;
    border: none;
    background: transparent;
    color: white;
    transition: all 0.3s ease;
    overflow: hidden;
}

/* 展开时显示搜索输入框 */
.search-input-expanded {
    width: 150px !important;
    opacity: 1 !important;
    padding: 0.375rem 0.75rem !important;
    margin-right: 0.5rem !important;
    border: 1px solid rgba(255,255,255,0.3) !important;
    background-color: rgba(255,255,255,0.1) !important;
}

/* 全局搜索输入框宽度调整 */
.form-control.search-input-mobile.search-input-expanded {
    width: calc(90px) !important;
}

/* 全局页脚样式 */
footer.bg-dark.text-light.py-4.mt-5 {
    background-color: #212529 !important;
    color: #f8f9fa !important;
    padding-top: 10px !important;
    padding-bottom: 0px !important;
    margin-top: 4rem !important;
}

.search-input-mobile::placeholder {
    color: rgba(255,255,255,0.7);
}

.search-input-mobile:focus {
    background-color: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.3);
    box-shadow: none;
    color: white;
    outline: none;
}

/* 搜索框展开动画 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 导航栏防夜间模式样式 */
.navbar {
    /* 强制导航栏颜色，防止夜间模式影响 */
    background-color: #0d6efd !important;
    color-scheme: light only !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    /* 防夜间模式影响 */
    color: #ffffff !important;
    color-scheme: light only !important;
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-size: 0.9rem;
    white-space: nowrap;
    transition: color 0.3s ease;
    /* 防夜间模式影响 */
    color: rgba(255, 255, 255, 0.75) !important;
    color-scheme: light only !important;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
    /* 防夜间模式影响悬停效果 */
    color: #ffffff !important;
    color-scheme: light only !important;
}

.navbar-nav .nav-link.active {
    /* 防夜间模式影响激活状态 */
    color: #ffffff !important;
    color-scheme: light only !important;
}

/* 导航栏按钮防夜间模式 */
.navbar .btn-outline-light {
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: rgba(255, 255, 255, 0.75) !important;
    color-scheme: light only !important;
}

.navbar .btn-outline-light:hover,
.navbar .btn-outline-light:focus {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: #ffffff !important;
    color: #ffffff !important;
    color-scheme: light only !important;
}

/* 搜索框防夜间模式 */
.navbar .form-control {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: #ffffff !important;
    color-scheme: light only !important;
}

.navbar .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6) !important;
}

.navbar .form-control:focus {
    background-color: rgba(255, 255, 255, 0.15) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
    color-scheme: light only !important;
}

/* 导航栏图标防夜间模式 */
.navbar-nav .nav-link i {
    color: inherit !important;
    color-scheme: light only !important;
}

/* 导航栏切换按钮防夜间模式（用于error.html） */
.navbar-toggler {
    border-color: rgba(255, 255, 255, 0.3) !important;
    color-scheme: light only !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

/* 强制防夜间模式 - 最高优先级 */
.navbar,
.navbar *,
.navbar .nav-link,
.navbar .nav-link *,
.navbar .btn,
.navbar .btn *,
.navbar .form-control,
.navbar .navbar-brand,
.navbar .navbar-toggler {
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

/* 确保所有导航栏元素都有正确的颜色 */
.navbar .nav-link,
.navbar .nav-link:visited {
    color: rgba(255, 255, 255, 0.75) !important;
}

.navbar .nav-link:hover,
.navbar .nav-link:focus,
.navbar .nav-link.active {
    color: #ffffff !important;
}

.navbar .btn-outline-light {
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: rgba(255, 255, 255, 0.75) !important;
}

.navbar .btn-outline-light:hover,
.navbar .btn-outline-light:focus {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: #ffffff !important;
    color: #ffffff !important;
}





.navbar .form-control {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: #ffffff !important;
}

.navbar .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6) !important;
}

.navbar .form-control:focus {
    background-color: rgba(255, 255, 255, 0.15) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
}



/* 导航栏在移动端不折叠的样式 */
.navbar .d-flex.w-100 {
    flex-wrap: nowrap;
}

.navbar-nav.flex-row .nav-item {
    margin-right: 0.5rem;
}

.navbar-nav.flex-row .nav-link {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}



/* 移动端响应式导航 */
@media (max-width: 768px) {
    .search-input-mobile {
        width: 120px;
        font-size: 0.8rem;
        min-width: 120px;
    }



    .navbar-nav .nav-link {
        font-size: 0.8rem;
        padding: 0.5rem 0.3rem !important;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }

    .navbar-nav.flex-row .nav-item {
        margin-right: 0.2rem;
    }

    .search-container {
        flex-shrink: 0;
    }

    .search-form {
        gap: 0.25rem;
    }
}

@media (max-width: 576px) {
    .search-input-mobile {
        width: 100px;
        font-size: 0.75rem;
        min-width: 100px;
    }



    .navbar-nav .nav-link {
        font-size: 0.75rem;
        padding: 0.4rem 0.2rem !important;
    }

    .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }

    .navbar-nav.flex-row .nav-item {
        margin-right: 0.1rem;
    }

    /* 在极小屏幕上隐藏图标文字，只显示图标 */
    .navbar-nav .nav-link i + * {
        display: none;
    }

    .search-form {
        gap: 0.1rem;
    }

    .navbar .d-flex.w-100 {
        flex-wrap: nowrap;
        overflow: hidden;
    }

    /* 搜索框展开时的特殊处理 */
    .search-form.show {
        position: absolute;
        right: 0;
        top: 100%;
        background: rgba(13, 110, 253, 0.95);
        padding: 0.5rem;
        border-radius: 0.375rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        z-index: 1000;
    }
}

/* 通用工具类 */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    ::-webkit-scrollbar-track {
        background: #2d2d2d;
    }
    
    ::-webkit-scrollbar-thumb {
        background: #555;
    }
    
    ::-webkit-scrollbar-thumb:hover {
        background: #777;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .alert,
    .modal {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}

/* 无障碍访问 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 主要内容区域样式 */
.main-content, main {
    flex: 1;
    margin-bottom: auto;
}

/* 页脚居中样式和底端定位 */
.bg-dark.text-light.py-4.mt-5 {
    font-size: 15px !important;
    text-align: center;
    padding: 10px !important;
    margin-top: auto !important;
    flex-shrink: 0;
    height: 65px !important;
}

.footer-single-container .spqk01,
.footer-single-container .spqk02 {
    text-align: center;
    margin: 0 auto;
}


.bg-dark.text-light.py-4.mt-5 .container {
    text-align: center;
}

.bg-dark.text-light.py-4.mt-5 .row {
    justify-content: center;
    text-align: center;
}

.bg-dark.text-light.py-4.mt-5 .col-md-6 {
    text-align: center;
}

.bg-dark.text-light.py-4.mt-5 .text-md-end {
    text-align: center !important;
}

/* video-info 容器内容左对齐 */
.video-info.bg-white.rounded-3.shadow-sm.p-4.mb-4 {
    text-align: left !important;
}

.video-info.bg-white.rounded-3.shadow-sm.p-4.mb-4 * {
    text-align: left !important;
}

/* 焦点样式 */
.btn:focus,
.form-control:focus,
.form-select:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .btn {
        border-width: 2px;
    }
    
    .card {
        border-width: 2px;
    }
    
    .navbar-nav .nav-link {
        font-weight: 600;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ========== 强力防夜间模式样式 ========== */

/* 覆盖所有可能的夜间模式媒体查询 */
@media (prefers-color-scheme: dark) {
    :root {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
        --bs-body-bg: #ffffff !important;
        --bs-body-color: #333333 !important;
        --bs-primary: #0d6efd !important;
        --bs-secondary: #6c757d !important;
        --bs-success: #198754 !important;
        --bs-info: #0dcaf0 !important;
        --bs-warning: #ffc107 !important;
        --bs-danger: #dc3545 !important;
        --bs-light: #f8f9fa !important;
        --bs-dark: #212529 !important;
    }

    html, body {
        background-color: #ffffff !important;
        color: #333333 !important;
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }

    /* 强制所有元素保持亮色主题 */
    *, *::before, *::after {
        background-color: inherit !important;
        color: inherit !important;
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }

    /* 确保所有容器元素 */
    div, section, article, main, header, footer, nav, aside, span, p, h1, h2, h3, h4, h5, h6 {
        background-color: transparent !important;
        color: inherit !important;
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }

    /* Bootstrap组件强制亮色 */
    .navbar, .card, .btn, .form-control, .modal, .alert, .dropdown-menu, .badge, .breadcrumb {
        filter: none !important;
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }

    /* 表格强制亮色 */
    .table, .table th, .table td {
        background-color: inherit !important;
        color: inherit !important;
        border-color: #dee2e6 !important;
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }

    /* 输入框强制亮色 */
    input, textarea, select, option {
        background-color: #ffffff !important;
        color: #333333 !important;
        border-color: #ced4da !important;
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }

    /* 链接强制颜色 */
    a, a:visited, a:hover, a:focus {
        color: #0d6efd !important;
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}

/* 强制覆盖任何可能的深色主题属性 */
[data-theme="dark"],
[data-bs-theme="dark"],
.dark-theme,
.theme-dark {
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
}

/* 防止浏览器自动应用深色样式 */
@media screen {
    html {
        color-scheme: light only !important;
        background-color: #ffffff !important;
    }

    body {
        background-color: #ffffff !important;
        color: #333333 !important;
    }
}

/* 移动端专用防护 */
@media screen and (max-width: 768px) {
    html, body {
        background-color: #ffffff !important;
        color: #333333 !important;
        color-scheme: light only !important;
    }

    * {
        color-scheme: light only !important;
    }
}

/* ========================================
   分页样式 - 全局统一样式
   ======================================== */

/* 分页容器 */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 3rem;
}

/* 分页链接基础样式 */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #667eea;
    font-weight: 500;
    transition: all 0.3s ease;
}

/* 激活状态样式 */
.pagination .page-item.active .page-link {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

/* 禁用状态样式 - 统一上一页和下一页的disabled样式，背景设置为白色 */
.pagination .page-item.disabled .page-link {
    background: #ffffff !important;
    border-color: #dee2e6 !important;
    color: #667eea !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
}

/* disabled状态的span元素样式（与disabled的a元素保持一致） */
.pagination .page-item.disabled span.page-link {
    background: #ffffff !important;
    border-color: #dee2e6 !important;
    color: #667eea !important;
    cursor: not-allowed !important;
}

/* 悬停效果 */
.pagination .page-link:hover:not(.disabled) {
    background: #f8f9fa;
    color: #495057;
    transform: translateY(-1px);
}

/* 响应式分页 */
@media (max-width: 768px) {
    .pagination .page-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    .pagination-container {
        margin-top: 2rem;
    }
}

/* 管理页面状态栏样式 */
.status-column {
    min-width: 80px;
}

.status-column .d-flex.flex-column {
    gap: 0.25rem;
}

.status-column .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    white-space: nowrap;
}

.status-column .badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
    border: 1px solid #ffb300;
}

.status-column .badge.bg-light {
    background-color: #f8f9fa !important;
    color: #6c757d !important;
    border: 1px solid #dee2e6 !important;
}

.status-column .badge.bg-light:hover {
    background-color: #e9ecef !important;
    color: #495057 !important;
    border-color: #adb5bd !important;
}

.status-column .badge.bg-success {
    background-color: #198754 !important;
    color: #fff !important;
}

.status-column .badge.bg-secondary {
    background-color: #6c757d !important;
    color: #fff !important;
}

/* 可点击的置顶状态样式 */
.clickable-pin-status {
    transition: all 0.2s ease;
}

.clickable-pin-status:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* ========== 最终防夜间模式保护 ========== */
/* 确保在任何情况下都不会应用夜间模式 */
@media screen {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}

/* 移动端专用防护 */
@media screen and (max-device-width: 768px) {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
        background-color: inherit !important;
        color: inherit !important;
    }
}

/* iOS Safari 专用防护 */
@supports (-webkit-touch-callout: none) {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}
