/**
 * 管理界面专用样式 - 强制防夜间模式
 * Admin Page Styles - Force Light Mode
 */

/* 管理页面防夜间模式设置 */
:root {
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

html, body {
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}





/* 视频缩略图 */
.video-thumbnail {
    width: 80px;
    height: 45px;
    object-fit: cover;
    border-radius: 4px;
}

/* 缩略图优化样式 */
.thumbnail-optimized {
    transition: opacity 0.3s ease-in-out;
    opacity: 0.7;
    background-color: #f8f9fa;
}

.thumbnail-optimized.loaded {
    opacity: 1;
    background-image: none;
}

/* 视频标题 */
.video-title {
    font-weight: 600;
    color: #2c3e50;
}

/* 视频描述 */
.video-description {
    color: #6c757d;
    font-size: 0.875rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 状态徽章 */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* 操作按钮 */
.action-btn {
    padding: 0.25rem 0.5rem;
    margin: 0 1px;
}

/* 搜索区域 */
.search-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

/* 表格容器 */
.table-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* 修复sticky定位的层级问题 */
.navbar.sticky-top {
    z-index: 1030 !important;
}

.table-responsive .table thead.sticky-top {
    z-index: 1020 !important;
}

.table-responsive {
    position: relative;
    z-index: 1010 !important;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}



/* 禁用的视频行 */
.video-row.disabled {
    opacity: 0.6;
    background-color: #f8f9fa;
}

/* 修改复选框的勾选标记颜色为黑色 */
.form-check-input:checked {
    background-color: #ffffff !important;
    border-color: #000000 !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23000000' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e") !important;
}

.form-check-input:checked:focus {
    background-color: #ffffff !important;
    border-color: #000000 !important;
    box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.25) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-section {
        padding: 1rem;
    }

    .video-thumbnail {
        width: 60px;
        height: 34px;
    }

    .action-btn {
        padding: 0.2rem 0.4rem;
        margin: 0;
    }
    .d-flex.gap-01 {
        background-color: #198754 !important;
    }

    /* 手机端添加按钮样式 - 确保图标和文字为白色 */
    .btn-success .fas.fa-plus,
    .btn-success {
        color: white !important;
    }

    .btn-success:hover .fas.fa-plus,
    .btn-success:hover {
        color: white !important;
    }

    .btn-success:focus .fas.fa-plus,
    .btn-success:focus {
        color: white !important;
    }

    .btn-success:active .fas.fa-plus,
    .btn-success:active {
        color: white !important;
    }

    .btn-success:visited .fas.fa-plus,
    .btn-success:visited {
        color: white !important;
    }
}

/* 打印样式 */
@media print {
    .search-section,
    .action-btn,
    .loading-overlay {
        display: none !important;
    }
    
    .table-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* ========== 管理页面最终防夜间模式保护 ========== */
@media screen {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}

@media screen and (max-device-width: 768px) {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}