/**
 * 佳茵轻康视频管理界面
 * 人性化的管理界面JavaScript功能
 * @version 2.0.0
 */

// 全局变量
let selectedVideos = new Set();
let allVideos = [];
let filteredVideos = [];

// 工具函数
const AdminUtils = {
    // 提示消息功能已移除
    showAlert: function(message, type = 'info', duration = 3000) {
        // 全局提示器已删除，改为控制台输出
        console.log(`[${type.toUpperCase()}] ${message}`);
    },



    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },




};

// API接口函数
const AdminApi = {
    async createVideo(videoData) {
        try {
            const response = await fetch('/api/videos', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(videoData)
            });
            return await response.json();
        } catch (error) {
            throw new Error('网络错误：' + error.message);
        }
    },

    // 上传视频文件
    async uploadVideo(file, onProgress) {
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/upload/video', {
                method: 'POST',
                body: formData
            });
            return await response.json();
        } catch (error) {
            throw new Error('视频上传失败：' + error.message);
        }
    },

    // 上传缩略图
    async uploadThumbnail(file) {
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/upload/thumbnail', {
                method: 'POST',
                body: formData
            });
            return await response.json();
        } catch (error) {
            throw new Error('缩略图上传失败：' + error.message);
        }
    },

    // 一次性上传视频和缩略图
    async uploadVideoWithThumbnail(formData) {
        try {
            const response = await fetch('/api/upload/video-with-thumbnail', {
                method: 'POST',
                body: formData
            });
            return await response.json();
        } catch (error) {
            throw new Error('视频上传失败：' + error.message);
        }
    },

    async updateVideo(id, videoData) {
        try {
            const response = await fetch(`/api/videos/${id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(videoData)
            });
            return await response.json();
        } catch (error) {
            throw new Error('网络错误：' + error.message);
        }
    },

    async deleteVideo(id) {
        try {
            const response = await fetch(`/api/videos/${id}`, {
                method: 'DELETE'
            });
            return await response.json();
        } catch (error) {
            throw new Error('网络错误：' + error.message);
        }
    },

    async getVideos(page = 0, size = 100) {
        try {
            const response = await fetch(`/api/videos?page=${page}&size=${size}`);
            return await response.json();
        } catch (error) {
            throw new Error('网络错误：' + error.message);
        }
    },

    async toggleVideoStatus(id, isActive) {
        try {
            const response = await fetch(`/api/videos/${id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ isActive: !isActive })
            });
            return await response.json();
        } catch (error) {
            throw new Error('网络错误：' + error.message);
        }
    },

    // 切换视频置顶状态
    async toggleVideoPin(id) {
        try {
            const response = await fetch(`/api/videos/${id}/pin`, {
                method: 'PUT'
            });
            return await response.json();
        } catch (error) {
            throw new Error('网络错误：' + error.message);
        }
    },

    // 设置视频置顶状态
    async setVideoPin(id, isPinned) {
        try {
            const response = await fetch(`/api/videos/${id}/pin/${isPinned}`, {
                method: 'PUT'
            });
            return await response.json();
        } catch (error) {
            throw new Error('网络错误：' + error.message);
        }
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeAdminPage();
    setupKeyboardShortcuts();
    // updateStatistics(); // 已删除统计卡片，不再需要更新统计
});

// 初始化管理页面
function initializeAdminPage() {
    console.log('🚀 初始化管理界面...');

    // 初始化搜索功能
    initializeSearch();

    // 初始化选择功能
    initializeSelection();

    // 初始化筛选功能
    initializeFilters();

    // 初始化删除确认
    initializeDeleteConfirmation();

    // 收集所有视频数据
    collectVideoData();

    AdminUtils.showAlert('管理界面已就绪 ✨', 'success', 2000);
}

// 收集视频数据
function collectVideoData() {
    const videoRows = document.querySelectorAll('.video-row');
    allVideos = Array.from(videoRows).map(row => ({
        id: row.dataset.videoId,
        element: row,
        title: row.querySelector('.video-title')?.textContent || '',
        description: row.querySelector('.video-description')?.textContent || '',
        isActive: !row.classList.contains('disabled')
    }));
    filteredVideos = [...allVideos];
    console.log(`📊 收集到 ${allVideos.length} 个视频`);
}

// 初始化搜索功能
function initializeSearch() {
    const searchInput = document.getElementById('adminSearch');
    if (searchInput) {
        searchInput.addEventListener('input', AdminUtils.debounce(function() {
            performSearch(this.value);
        }, 300));

        // 搜索框获得焦点时的提示
        searchInput.addEventListener('focus', function() {
            this.placeholder = '输入关键词开始搜索...';
        });

        searchInput.addEventListener('blur', function() {
            this.placeholder = '输入标题或描述关键词...';
        });
    }
}

// 执行搜索
function performSearch(keyword) {
    if (!keyword.trim()) {
        // 显示所有视频
        filteredVideos = [...allVideos];
        showAllVideos();
        updateFilteredCount();
        return;
    }

    const searchTerm = keyword.toLowerCase();
    filteredVideos = allVideos.filter(video =>
        video.title.toLowerCase().includes(searchTerm) ||
        video.description.toLowerCase().includes(searchTerm)
    );

    // 显示搜索结果
    showFilteredVideos();
    updateFilteredCount();

    if (filteredVideos.length === 0) {
        AdminUtils.showAlert(`没有找到包含"${keyword}"的视频 🔍`, 'info');
    }
}

// 显示所有视频
function showAllVideos() {
    allVideos.forEach(video => {
        video.element.style.display = '';
    });
}

// 显示筛选后的视频
function showFilteredVideos() {
    const filteredIds = new Set(filteredVideos.map(v => v.id));

    allVideos.forEach(video => {
        video.element.style.display = filteredIds.has(video.id) ? '' : 'none';
    });
}

// 更新筛选计数
function updateFilteredCount() {
    const filteredCountElement = document.getElementById('filteredCount');
    if (filteredCountElement) {
        if (filteredVideos.length !== allVideos.length) {
            filteredCountElement.style.display = 'inline';
            filteredCountElement.querySelector('span').textContent = filteredVideos.length;
        } else {
            filteredCountElement.style.display = 'none';
        }
    }
}

// 初始化选择功能
function initializeSelection() {
    console.log('🔧 初始化选择功能...');

    // 全选复选框
    const selectAllCheckbox = document.getElementById('selectAllTable');
    console.log('全选复选框:', selectAllCheckbox);

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            console.log('全选复选框状态改变:', this.checked);
            const isChecked = this.checked;
            const visibleCheckboxes = document.querySelectorAll('.video-checkbox:not([style*="display: none"])');
            console.log('找到视频复选框数量:', visibleCheckboxes.length);

            visibleCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                updateVideoSelection(checkbox.value, isChecked);
            });

            updateSelectionUI();
        });
    } else {
        console.error('❌ 未找到全选复选框 #selectAllTable');
    }

    // 单个视频复选框
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('video-checkbox')) {
            updateVideoSelection(e.target.value, e.target.checked);
            updateSelectionUI();
            updateSelectAllState();
        }
    });

    // 初始化切换全选按钮状态
    updateToggleSelectAllButton();
}

// 更新视频选择状态
function updateVideoSelection(videoId, isSelected) {
    if (isSelected) {
        selectedVideos.add(videoId);
    } else {
        selectedVideos.delete(videoId);
    }
}

// 更新选择相关UI
function updateSelectionUI() {
    const selectedCount = selectedVideos.size;
    console.log('🔄 更新选择UI，当前选中数量:', selectedCount);

    // 更新选中信息显示（保留这个功能，因为它在表格底部显示选中数量）
    const selectedInfo = document.getElementById('selectedInfo');
    if (selectedInfo) {
        if (selectedCount > 0) {
            selectedInfo.style.display = 'inline';
            selectedInfo.querySelector('span').textContent = selectedCount;
        } else {
            selectedInfo.style.display = 'none';
        }
    }

    // 更新批量操作按钮状态
    const batchButtons = document.querySelectorAll('.batch-operation');
    console.log('找到批量操作按钮数量:', batchButtons.length);
    batchButtons.forEach(button => {
        button.disabled = selectedCount === 0;
        console.log('按钮状态更新:', button.textContent.trim(), '禁用:', button.disabled);
    });

    // 高亮选中的行
    document.querySelectorAll('.video-row').forEach(row => {
        const videoId = row.dataset.videoId;
        if (selectedVideos.has(videoId)) {
            row.classList.add('table-primary');
        } else {
            row.classList.remove('table-primary');
        }
    });

    // 更新切换全选按钮状态
    updateToggleSelectAllButton();
}

// 更新全选状态
function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('selectAllTable');
    if (!selectAllCheckbox) return;

    const visibleCheckboxes = document.querySelectorAll('.video-checkbox:not([style*="display: none"])');
    const checkedBoxes = document.querySelectorAll('.video-checkbox:checked:not([style*="display: none"])');

    if (checkedBoxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedBoxes.length === visibleCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }
}

// 选择操作函数
function selectAll() {
    const visibleCheckboxes = document.querySelectorAll('.video-checkbox:not([style*="display: none"])');
    visibleCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
        updateVideoSelection(checkbox.value, true);
    });
    updateSelectionUI();
    updateSelectAllState();
    updateToggleSelectAllButton();
    AdminUtils.showAlert(`已选择 ${selectedVideos.size} 个视频`, 'info');
}

// 切换全选/取消全选
function toggleSelectAll() {
    const visibleCheckboxes = document.querySelectorAll('.video-checkbox:not([style*="display: none"])');
    const checkedCount = Array.from(visibleCheckboxes).filter(cb => cb.checked).length;
    const totalCount = visibleCheckboxes.length;

    if (checkedCount === totalCount && totalCount > 0) {
        // 当前是全选状态，执行取消全选
        selectNone();
    } else {
        // 当前不是全选状态，执行全选
        selectAll();
    }
}

// 更新切换全选按钮的状态
function updateToggleSelectAllButton() {
    const toggleBtn = document.getElementById('toggleSelectAllBtn');
    const toggleIcon = document.getElementById('toggleSelectAllIcon');
    const toggleText = document.getElementById('toggleSelectAllText');

    if (!toggleBtn || !toggleIcon || !toggleText) return;

    const visibleCheckboxes = document.querySelectorAll('.video-checkbox:not([style*="display: none"])');
    const checkedCount = Array.from(visibleCheckboxes).filter(cb => cb.checked).length;
    const totalCount = visibleCheckboxes.length;

    if (checkedCount === totalCount && totalCount > 0) {
        // 全选状态
        toggleIcon.className = 'fas fa-square me-1';
        toggleText.textContent = '取消';
        toggleBtn.className = 'btn btn-sm btn-outline-primary';
    } else {
        // 非全选状态
        toggleIcon.className = 'fas fa-check-square me-1';
        toggleText.textContent = '全选';
        toggleBtn.className = 'btn btn-sm btn-outline-secondary';
    }
}

function selectNone() {
    document.querySelectorAll('.video-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    selectedVideos.clear();
    updateSelectionUI();
    updateSelectAllState();
    updateToggleSelectAllButton();
    AdminUtils.showAlert('已取消所有选择', 'info');
}

function selectInvert() {
    const visibleCheckboxes = document.querySelectorAll('.video-checkbox:not([style*="display: none"])');
    visibleCheckboxes.forEach(checkbox => {
        checkbox.checked = !checkbox.checked;
        updateVideoSelection(checkbox.value, checkbox.checked);
    });
    updateSelectionUI();
    updateSelectAllState();
    updateToggleSelectAllButton();
    AdminUtils.showAlert(`已反选，当前选择 ${selectedVideos.size} 个视频`, 'info');
}

// 初始化筛选功能
function initializeFilters() {
    const statusFilter = document.getElementById('statusFilter');

    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }
}

// 应用筛选器
function applyFilters() {
    const searchValue = document.getElementById('adminSearch')?.value || '';
    const statusValue = document.getElementById('statusFilter')?.value || '';

    // 构建URL参数
    const params = new URLSearchParams();
    if (searchValue) params.append('search', searchValue);
    if (statusValue) params.append('status', statusValue);



    // 跳转到筛选后的页面
    const newUrl = `/admin${params.toString() ? '?' + params.toString() : ''}`;
    window.location.href = newUrl;
}

// 重置筛选器
function resetFilters() {
    document.getElementById('adminSearch').value = '';
    document.getElementById('statusFilter').value = '';

    AdminUtils.showAlert('筛选器已重置', 'info');
    setTimeout(() => {
        window.location.href = '/admin';
    }, 1000);
}

// 清除搜索
function clearSearch() {
    const searchInput = document.getElementById('adminSearch');
    if (searchInput) {
        searchInput.value = '';
        performSearch('');
        searchInput.focus();
    }
}

// 视频操作函数



// 确认删除视频
function confirmDeleteVideo(videoId, videoTitle) {
    // 检查视频是否为禁用状态
    const videoRow = document.querySelector(`tr[data-video-id="${videoId}"]`);
    if (videoRow && !videoRow.classList.contains('disabled')) {
        AdminUtils.showAlert('只能删除已禁用状态的视频。请先禁用视频再进行删除操作。', 'warning');
        return;
    }

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const titleElement = document.getElementById('deleteVideoTitle');

    if (titleElement) {
        titleElement.textContent = videoTitle;
    }

    // 存储要删除的视频ID
    window.currentDeleteVideoId = videoId;

    modal.show();
}

// 切换视频状态
async function toggleVideoStatus(videoId, currentStatus) {
    try {
        const newStatus = !currentStatus;
        const statusText = currentStatus ? '禁用' : '启用';

        // 立即更新UI状态，提供即时反馈
        updateVideoStatusDisplay(videoId, newStatus);
        updateVideoRowStyle(videoId, newStatus);

        const response = await AdminApi.toggleVideoStatus(videoId, currentStatus);

        if (response.success) {
            AdminUtils.showAlert(`视频已${statusText} ✅`, 'success');
        } else {
            // 如果API调用失败，恢复原状态
            updateVideoStatusDisplay(videoId, currentStatus);
            updateVideoRowStyle(videoId, currentStatus);
            throw new Error(response.message || '操作失败');
        }
    } catch (error) {
        // 如果出现错误，恢复原状态
        updateVideoStatusDisplay(videoId, currentStatus);
        updateVideoRowStyle(videoId, currentStatus);
        AdminUtils.showAlert('操作失败：' + error.message, 'danger');
    }
}

// 更新视频状态显示
function updateVideoStatusDisplay(videoId, newStatus) {
    const videoRow = document.querySelector(`tr[data-video-id="${videoId}"]`);
    if (!videoRow) return;

    // 找到状态标识元素
    const statusBadge = videoRow.querySelector('.clickable-status');
    if (!statusBadge) return;

    // 更新状态标识
    if (newStatus) {
        // 启用状态
        statusBadge.className = 'status-badge badge bg-success clickable-status';
        statusBadge.innerHTML = '<i class="fas fa-check me-1"></i>启用';
        statusBadge.title = '点击切换为禁用状态';
    } else {
        // 禁用状态
        statusBadge.className = 'status-badge badge bg-secondary clickable-status';
        statusBadge.innerHTML = '<i class="fas fa-pause me-1"></i>禁用';
        statusBadge.title = '点击切换为启用状态';
    }

    // 更新data属性
    statusBadge.dataset.isActive = newStatus.toString();
}

// 更新视频行样式
function updateVideoRowStyle(videoId, newStatus) {
    const videoRow = document.querySelector(`tr[data-video-id="${videoId}"]`);
    if (!videoRow) return;

    if (newStatus) {
        // 启用状态 - 移除disabled类
        videoRow.classList.remove('disabled');
    } else {
        // 禁用状态 - 添加disabled类
        videoRow.classList.add('disabled');
    }

    // 更新删除按钮显示
    updateDeleteButtonVisibility(videoId, newStatus);
}

// 更新删除按钮显示
function updateDeleteButtonVisibility(videoId, newStatus) {
    const videoRow = document.querySelector(`tr[data-video-id="${videoId}"]`);
    if (!videoRow) return;

    // 查找删除按钮（使用class选择器）
    const deleteButton = videoRow.querySelector('.delete-btn');

    if (deleteButton) {
        if (newStatus) {
            // 启用状态 - 隐藏删除按钮
            deleteButton.style.display = 'none';
        } else {
            // 禁用状态 - 显示删除按钮
            deleteButton.style.display = 'inline-block';
        }
    }
}



// 批量启用
async function batchEnable() {
    console.log('🚀 批量启用被调用，选中视频数量:', selectedVideos.size);
    console.log('选中的视频ID:', Array.from(selectedVideos));

    if (selectedVideos.size === 0) {
        AdminUtils.showAlert('请先选择要启用的视频', 'warning');
        return;
    }

    if (!confirm(`确定要启用选中的 ${selectedVideos.size} 个视频吗？`)) {
        return;
    }

    try {

        let successCount = 0;

        for (const videoId of selectedVideos) {
            try {
                // 直接调用API设置为启用状态
                const response = await fetch(`/api/videos/${videoId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ isActive: true })
                });
                const result = await response.json();
                if (result.success) successCount++;
            } catch (error) {
                console.error(`启用视频 ${videoId} 失败:`, error);
            }
        }

        AdminUtils.showAlert(`成功启用 ${successCount} 个视频 🎉`, 'success');
        setTimeout(() => window.location.reload(), 1500);

    } catch (error) {
        AdminUtils.showAlert('批量启用失败：' + error.message, 'danger');
    }
}

// 批量禁用
async function batchDisable() {
    if (selectedVideos.size === 0) {
        AdminUtils.showAlert('请先选择要禁用的视频', 'warning');
        return;
    }

    if (!confirm(`确定要禁用选中的 ${selectedVideos.size} 个视频吗？`)) {
        return;
    }

    try {

        let successCount = 0;

        for (const videoId of selectedVideos) {
            try {
                // 直接调用API设置为禁用状态
                const response = await fetch(`/api/videos/${videoId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ isActive: false })
                });
                const result = await response.json();
                if (result.success) successCount++;
            } catch (error) {
                console.error(`禁用视频 ${videoId} 失败:`, error);
            }
        }

        AdminUtils.showAlert(`成功禁用 ${successCount} 个视频 ⏸️`, 'success');
        setTimeout(() => window.location.reload(), 1500);

    } catch (error) {
        AdminUtils.showAlert('批量禁用失败：' + error.message, 'danger');
    }
}

// 批量删除
async function batchDelete() {
    if (selectedVideos.size === 0) {
        AdminUtils.showAlert('请先选择要删除的视频', 'warning');
        return;
    }

    // 检查选中的视频中是否有启用状态的视频
    const enabledVideos = [];
    const disabledVideos = [];

    for (const videoId of selectedVideos) {
        const videoRow = document.querySelector(`tr[data-video-id="${videoId}"]`);
        if (videoRow) {
            const isActive = !videoRow.classList.contains('disabled');
            if (isActive) {
                const title = videoRow.querySelector('.video-title')?.textContent || `ID: ${videoId}`;
                enabledVideos.push(title);
            } else {
                disabledVideos.push(videoId);
            }
        }
    }

    if (enabledVideos.length > 0) {
        AdminUtils.showAlert(`无法删除启用状态的视频。只能删除已禁用的视频。\n\n启用状态的视频：\n${enabledVideos.slice(0, 3).join('\n')}${enabledVideos.length > 3 ? '\n...' : ''}`, 'warning');
        return;
    }

    if (disabledVideos.length === 0) {
        AdminUtils.showAlert('没有可删除的视频（只能删除已禁用的视频）', 'warning');
        return;
    }

    const confirmMessage = `⚠️ 危险操作确认\n\n您即将永久删除 ${disabledVideos.length} 个已禁用的视频。\n此操作不可撤销，确定继续吗？`;

    if (!confirm(confirmMessage)) {
        return;
    }

    try {

        let successCount = 0;
        let failCount = 0;

        const deletedVideos = [];

        for (const videoId of disabledVideos) {
            try {
                const response = await AdminApi.deleteVideo(videoId);
                if (response.success) {
                    successCount++;
                    deletedVideos.push(videoId);
                } else {
                    failCount++;
                }
            } catch (error) {
                failCount++;
                console.error(`删除视频 ${videoId} 失败:`, error);
            }
        }

        // 动态移除成功删除的视频行
        deletedVideos.forEach(videoId => {
            removeVideoRow(videoId);
        });

        if (successCount > 0) {
            AdminUtils.showAlert(`成功删除 ${successCount} 个视频 🗑️`, 'success');
        }

        if (failCount > 0) {
            AdminUtils.showAlert(`${failCount} 个视频删除失败`, 'warning');
        }

        // 清空选择
        selectedVideos.clear();
        updateBatchActions();

        // 如果所有视频都被删除了，刷新页面
        setTimeout(() => {
            const remainingRows = document.querySelectorAll('tbody tr[data-video-id]');
            if (remainingRows.length === 0) {
                window.location.reload();
            }
        }, 1000);

    } catch (error) {
        AdminUtils.showAlert('批量删除失败：' + error.message, 'danger');
    }
}

// 动态移除视频行
function removeVideoRow(videoId) {
    const videoRow = document.querySelector(`tr[data-video-id="${videoId}"]`);
    if (videoRow) {
        // 添加淡出动画
        videoRow.style.transition = 'opacity 0.5s ease';
        videoRow.style.opacity = '0';

        // 500ms后移除元素
        setTimeout(() => {
            videoRow.remove();

            // 从选中列表中移除
            if (selectedVideos.has(videoId)) {
                selectedVideos.delete(videoId);
                updateBatchActions();
            }

            // 检查是否还有视频
            const remainingRows = document.querySelectorAll('tbody tr[data-video-id]');
            if (remainingRows.length === 0) {
                // 如果没有视频了，显示空状态或刷新页面
                setTimeout(() => window.location.reload(), 1000);
            }
        }, 500);
    }
}

// 初始化删除确认
function initializeDeleteConfirmation() {
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', async function() {
            const videoId = window.currentDeleteVideoId;
            if (!videoId) return;

            try {


                const response = await AdminApi.deleteVideo(videoId);

                if (response.success) {
                    AdminUtils.showAlert('视频删除成功 🗑️', 'success');

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
                    modal.hide();

                    // 动态移除视频行
                    removeVideoRow(videoId);

                    // 清除当前删除的视频ID
                    window.currentDeleteVideoId = null;
                } else {
                    throw new Error(response.message || '删除失败');
                }
            } catch (error) {
                AdminUtils.showAlert('删除失败：' + error.message, 'danger');
            }
        });
    }
}

// 更新统计信息 - 已删除统计卡片，此函数不再需要
// function updateStatistics() {
//     // 统计卡片已被删除，不再需要更新统计信息
// }

// 设置键盘快捷键
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+A 全选
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            selectAll();
        }

        // Delete 删除选中
        if (e.key === 'Delete' && selectedVideos.size > 0) {
            e.preventDefault();
            batchDelete();
        }

        // Escape 取消选择
        if (e.key === 'Escape') {
            selectNone();
        }

        // F5 刷新（允许默认行为）
        if (e.key === 'F5') {
            AdminUtils.showAlert('正在刷新页面...', 'info', 1000);
        }
    });
}

// 其他辅助功能

// 刷新页面函数已删除 - 按钮已移除

// 显示帮助 - 快速操作按钮已移除，但helpModal仍然存在
function showHelp() {
    AdminUtils.showAlert('快速帮助按钮已移除，如需帮助请联系管理员', 'info');
    // 注意：helpModal仍然存在，但无法通过按钮访问
}

// 导出数据 - 快速操作按钮已移除
function exportData() {
    AdminUtils.showAlert('快速导出按钮已移除，导出功能不再可用', 'warning');
}

// 导入数据 - 快速操作按钮已移除
function importData() {
    AdminUtils.showAlert('快速导入按钮已移除，导入功能不再可用', 'warning');
}

// 预览视频（如果在添加/编辑页面）
function previewVideo() {
    const videoUrl = document.getElementById('videoUrl')?.value;
    if (!videoUrl) {
        AdminUtils.showAlert('请先输入视频链接', 'warning');
        return;
    }

    // 在新窗口中打开视频预览
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>视频预览 - 佳茵轻康</title>
            <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">
            <style>
                body {
                    margin: 0;
                    padding: 20px;
                    background: #f8f9fa;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 10px;
                    padding: 20px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .video-js {
                    width: 100%;
                    height: 400px;
                    border-radius: 8px;
                }
                h1 {
                    color: #333;
                    margin-bottom: 20px;
                    text-align: center;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎬 视频预览</h1>
                <video id="preview-player" class="video-js vjs-default-skin" controls preload="auto">
                    <source src="${videoUrl}" type="video/mp4">
                    <p>您的浏览器不支持视频播放。</p>
                </video>
            </div>
            <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>
            <script>
                videojs('preview-player', {
                    fluid: true,
                    responsive: true,
                    playbackRates: [0.5, 1, 1.25, 1.5, 2]
                });
            </script>
        </body>
        </html>
    `);

    AdminUtils.showAlert('视频预览已在新窗口打开 🎬', 'success');
}

// 表单提交函数（用于添加/编辑页面）

/**
 * 提交添加视频表单
 */
async function submitAddVideo(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const videoData = Object.fromEntries(formData.entries());

    try {
        // 验证表单
        validateTitle(videoData.title);
        validateVideoUrl(videoData.videoUrl);
        validateDescription(videoData.description);


        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '保存中...';
        submitBtn.disabled = true;

        // 提交数据
        const response = await AdminApi.createVideo(videoData);

        if (response.success) {
            AdminUtils.showAlert('视频添加成功！🎉', 'success');
            setTimeout(() => {
                window.location.href = '/admin';
            }, 1500);
        } else {
            throw new Error(response.message || '添加失败');
        }
    } catch (error) {
        AdminUtils.showAlert(error.message, 'danger');

        // 恢复按钮状态
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }
}

/**
 * 提交编辑视频表单
 */
async function submitEditVideo(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const videoData = Object.fromEntries(formData.entries());
    const videoId = document.getElementById('videoId').value;

    try {
        // 验证表单
        validateTitle(videoData.title);
        validateVideoUrl(videoData.videoUrl);
        validateDescription(videoData.description);


        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '保存中...';
        submitBtn.disabled = true;

        // 提交数据
        const response = await AdminApi.updateVideo(videoId, videoData);

        if (response.success) {
            AdminUtils.showAlert('视频更新成功！✨', 'success');
            setTimeout(() => {
                window.location.href = '/admin';
            }, 1500);
        } else {
            throw new Error(response.message || '更新失败');
        }
    } catch (error) {
        AdminUtils.showAlert(error.message, 'danger');

        // 恢复按钮状态
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }
}

/**
 * 删除当前视频（用于编辑页面）
 */
function deleteCurrentVideo() {
    const videoId = document.getElementById('videoId')?.value;
    const title = document.getElementById('title')?.value || '此视频';

    if (confirm(`确定要删除视频"${title}"吗？此操作不可撤销。`)) {
        confirmDeleteVideo(videoId, title);
    }
}

// 表单验证函数

/**
 * 验证标题
 */
function validateTitle(title) {
    if (!title || title.trim() === '') {
        throw new Error('视频标题不能为空 📝');
    }
    if (title.length > 200) {
        throw new Error('视频标题不能超过200个字符 📏');
    }
    return true;
}

/**
 * 验证视频URL
 */
function validateVideoUrl(url) {
    if (!url || url.trim() === '') {
        throw new Error('视频链接不能为空 🔗');
    }

    // 简单的URL格式验证
    const urlPattern = /^https?:\/\/.+/;
    if (!urlPattern.test(url)) {
        throw new Error('请输入有效的视频链接（以http://或https://开头）🌐');
    }

    return true;
}

/**
 * 验证描述
 */
function validateDescription(description) {
    if (description && description.length > 500) {
        throw new Error('视频描述不能超过500个字符 📄');
    }
    return true;
}

// 控制台输出欢迎信息
console.log(`
🎬 佳茵轻康视频管理系统
📅 版本: 2.0.0
🚀 状态: 已就绪
💡 提示: 使用 Ctrl+A 全选，Delete 删除，Esc 取消选择
`);

/**
 * 切换视频置顶状态
 */
async function toggleVideoPin(videoId, isPinned, videoTitle) {
    try {
        console.log(`开始切换视频 ${videoId} 的置顶状态，当前状态: ${isPinned}`);

        // 立即更新UI状态，提供即时反馈
        const newPinStatus = !isPinned;
        updatePinBadgeImmediate(videoId, newPinStatus);

        const result = await AdminApi.toggleVideoPin(videoId);

        if (result.success) {
            const statusText = newPinStatus ? '已置顶到首页' : '已取消置顶';
            AdminUtils.showAlert(`${statusText} ✅`, 'success');
            console.log(`置顶状态切换成功，新状态: ${newPinStatus}`);
        } else {
            // 如果API调用失败，恢复原状态
            updatePinBadgeImmediate(videoId, isPinned);
            AdminUtils.showAlert(result.message || '置顶操作失败', 'error');
        }
    } catch (error) {
        // 如果出现错误，恢复原状态
        updatePinBadgeImmediate(videoId, isPinned);
        console.error('置顶操作失败:', error);
        AdminUtils.showAlert('置顶操作失败: ' + error.message, 'error');
    }
}

/**
 * 更新置顶按钮状态
 */
function updatePinButtonStatus(videoId, isPinned) {
    const pinBtn = document.querySelector(`[data-video-id="${videoId}"].pin-btn`);
    if (pinBtn) {
        pinBtn.dataset.isPinned = isPinned.toString();
        pinBtn.title = isPinned ? '取消置顶' : '置顶到首页';

        const icon = pinBtn.querySelector('i');
        if (icon) {
            if (isPinned) {
                icon.className = 'fas fa-thumbtack text-warning';
            } else {
                icon.className = 'fas fa-thumbtack';
            }
        }
    }
}

/**
 * 更新置顶标识（现在位于状态栏下方，支持两种状态切换）
 */
function updatePinBadge(videoId, isPinned) {
    const videoRow = document.querySelector(`[data-video-id="${videoId}"]`);
    if (videoRow) {
        // 查找状态栏中的flex容器
        const statusCell = videoRow.querySelector('td:nth-child(5)'); // 状态列是第5列
        const statusContainer = statusCell ? statusCell.querySelector('.d-flex.flex-column') : null;

        if (statusContainer) {
            // 移除现有的置顶标识
            const existingPinBadges = statusContainer.querySelectorAll('.clickable-pin-status');
            existingPinBadges.forEach(badge => badge.remove());

            // 创建新的置顶标识
            const pinBadge = document.createElement('span');
            pinBadge.className = 'clickable-pin-status';
            pinBadge.dataset.videoId = videoId;
            pinBadge.dataset.isPinned = isPinned.toString();
            pinBadge.style.cursor = 'pointer';

            if (isPinned) {
                // 已置顶状态
                pinBadge.className += ' badge bg-warning text-dark';
                pinBadge.title = '点击取消置顶';
                pinBadge.innerHTML = '<i class="fas fa-thumbtack me-1"></i>置顶';
            } else {
                // 未置顶状态
                pinBadge.className += ' badge bg-light text-dark border';
                pinBadge.title = '点击设置为置顶';
                pinBadge.innerHTML = '<i class="fas fa-thumbtack me-1"></i>未置顶';
            }

            // 添加点击事件
            pinBadge.onclick = function() {
                const videoTitle = videoRow.querySelector('.video-title').textContent;
                toggleVideoPin(videoId, isPinned, videoTitle);
            };

            statusContainer.appendChild(pinBadge);
        }
    }
}

/**
 * 立即更新置顶标识状态（用于提供即时反馈）
 */
function updatePinBadgeImmediate(videoId, isPinned) {
    const videoRow = document.querySelector(`tr[data-video-id="${videoId}"]`);
    if (!videoRow) return;

    // 查找现有的置顶标识
    const existingPinBadge = videoRow.querySelector('.clickable-pin-status');
    if (!existingPinBadge) return;

    // 更新data属性
    existingPinBadge.dataset.isPinned = isPinned.toString();
    existingPinBadge.dataset.videoTitle = videoRow.querySelector('.video-title')?.textContent || '';

    if (isPinned) {
        // 已置顶状态
        existingPinBadge.className = 'badge bg-warning text-dark clickable-pin-status';
        existingPinBadge.title = '点击取消置顶';
        existingPinBadge.innerHTML = '<i class="fas fa-thumbtack me-1"></i>置顶';
    } else {
        // 未置顶状态
        existingPinBadge.className = 'badge bg-light text-dark border clickable-pin-status';
        existingPinBadge.title = '点击设置为置顶';
        existingPinBadge.innerHTML = '<i class="fas fa-thumbtack me-1"></i>未置顶';
    }

    // 更新点击事件处理器
    existingPinBadge.onclick = function() {
        const videoTitle = videoRow.querySelector('.video-title')?.textContent || '';
        toggleVideoPin(videoId, isPinned, videoTitle);
    };
}

/**
 * 简单的加载状态管理函数
 */
function showLoading() {
    // 可以在这里添加加载动画或禁用按钮等逻辑
    console.log('显示加载状态');
}

function hideLoading() {
    // 可以在这里隐藏加载动画或启用按钮等逻辑
    console.log('隐藏加载状态');
}

/**
 * ID展开/折叠功能
 * @param {HTMLElement} element - 被点击的ID容器元素
 */
function toggleIdDisplay(element) {
    const isExpanded = element.classList.contains('id-expanded');
    const fullId = element.dataset.fullId;
    const displayText = element.querySelector('.id-display-text');
    const fullText = element.querySelector('.id-full-text');
    const icon = element.querySelector('.id-toggle-icon');

    if (isExpanded) {
        // 折叠ID
        element.classList.remove('id-expanded');
        element.classList.add('id-collapsed');
        displayText.textContent = fullId.substring(0, 4) + '...';
        displayText.style.display = 'inline';
        fullText.style.display = 'none';
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
        element.title = '点击展开完整ID';
    } else {
        // 展开ID
        element.classList.remove('id-collapsed');
        element.classList.add('id-expanded');
        displayText.style.display = 'none';
        fullText.style.display = 'block';
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
        element.title = '点击折叠ID';
    }
}

/**
 * 批量折叠所有ID
 */
function collapseAllIds() {
    const idContainers = document.querySelectorAll('.id-container.id-expanded');
    idContainers.forEach(container => {
        toggleIdDisplay(container);
    });
}

/**
 * 批量展开所有ID
 */
function expandAllIds() {
    const idContainers = document.querySelectorAll('.id-container.id-collapsed');
    idContainers.forEach(container => {
        toggleIdDisplay(container);
    });
}

/**
 * 复制ID到剪贴板
 * @param {string} videoId - 要复制的视频ID
 */
function copyVideoId(videoId) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(videoId).then(() => {
            AdminUtils.showAlert('视频ID已复制到剪贴板', 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            AdminUtils.showAlert('复制失败，请手动复制', 'error');
        });
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = videoId;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            AdminUtils.showAlert('视频ID已复制到剪贴板', 'success');
        } catch (err) {
            console.error('复制失败:', err);
            AdminUtils.showAlert('复制失败，请手动复制', 'error');
        }
        document.body.removeChild(textArea);
    }
}

// 导出全局函数供HTML调用
window.AdminUtils = AdminUtils;
window.AdminApi = AdminApi;
window.toggleVideoPin = toggleVideoPin;
window.updatePinBadgeImmediate = updatePinBadgeImmediate;
window.showLoading = showLoading;
window.hideLoading = hideLoading;
window.toggleIdDisplay = toggleIdDisplay;
window.collapseAllIds = collapseAllIds;
window.expandAllIds = expandAllIds;
window.copyVideoId = copyVideoId;


