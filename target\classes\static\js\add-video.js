/**
 * 添加视频页面专用JavaScript功能
 * <AUTHOR>
 * @version 1.0.0
 */

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeAddVideoPage();
});

/**
 * 初始化添加视频页面
 */
function initializeAddVideoPage() {
    const form = document.getElementById('addVideoForm');
    if (form) {
        form.addEventListener('submit', handleVideoUpload);
    }

    // 添加URL输入监听器
    addUrlInputListeners();

    console.log('添加视频页面初始化完成');
}

/**
 * 处理视频添加
 * @param {Event} event - 表单提交事件
 */
async function handleVideoUpload(event) {
    event.preventDefault();

    const form = event.target;
    const submitBtn = form.querySelector('button[type="submit"]');
    const progressContainer = document.getElementById('uploadProgress');
    const progressBar = progressContainer.querySelector('.progress-bar');

    try {
        // 验证表单
        if (!validateForm(form)) {
            return;
        }

        // 显示处理进度
        progressContainer.style.display = 'block';
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';

        // 模拟进度更新
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress > 90) progress = 90;
            progressBar.style.width = progress + '%';
        }, 200);

        // 构建视频数据
        const videoData = {
            title: form.querySelector('#title').value.trim(),
            description: form.querySelector('#description').value.trim(),
            videoUrl: form.querySelector('#videoUrl').value.trim(),
            thumbnailUrl: form.querySelector('#thumbnailUrl').value.trim() || null,
            videoFormat: form.querySelector('#videoFormat').value || null,
            resolution: form.querySelector('#resolution').value || null
        };

        // 提交视频数据
        const response = await fetch('/api/videos', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(videoData)
        });

        clearInterval(progressInterval);
        progressBar.style.width = '100%';

        const result = await response.json();

        if (result.success) {
            showAlert('视频添加成功！🎉', 'success');
            setTimeout(() => {
                window.location.href = '/admin';
            }, 1500);
        } else {
            throw new Error(result.message || '添加失败');
        }

    } catch (error) {
        console.error('添加失败:', error);
        showAlert('添加失败: ' + error.message, 'danger');

        // 重置状态
        progressContainer.style.display = 'none';
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>保存视频';
    }
}

/**
 * 表单验证
 * @param {HTMLFormElement} form - 表单元素
 * @returns {boolean} 验证结果
 */
function validateForm(form) {
    const videoUrl = form.querySelector('#videoUrl');
    const title = form.querySelector('#title');
    const thumbnailUrl = form.querySelector('#thumbnailUrl');

    // 验证视频URL
    if (!videoUrl.value.trim()) {
        showAlert('请输入视频URL地址', 'warning');
        return false;
    }

    // 验证URL格式
    try {
        new URL(videoUrl.value.trim());
    } catch (e) {
        showAlert('请输入有效的视频URL地址', 'warning');
        return false;
    }

    // 验证视频URL是否为支持的格式
    const videoUrlValue = videoUrl.value.trim().toLowerCase();
    const supportedVideoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v'];
    const hasValidExtension = supportedVideoExtensions.some(ext => videoUrlValue.includes(ext));
    if (!hasValidExtension) {
        showAlert('视频URL应包含支持的格式扩展名（mp4、avi、mov、wmv、flv、webm、mkv、m4v）', 'warning');
        return false;
    }

    // 验证标题
    if (!title.value.trim()) {
        showAlert('请输入视频标题', 'warning');
        return false;
    }

    // 验证缩略图URL（如果提供）
    if (thumbnailUrl.value.trim()) {
        try {
            new URL(thumbnailUrl.value.trim());
        } catch (e) {
            showAlert('请输入有效的缩略图URL地址', 'warning');
            return false;
        }

        // 验证缩略图URL是否为支持的格式
        const thumbnailUrlValue = thumbnailUrl.value.trim().toLowerCase();
        const supportedImageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
        const hasValidImageExtension = supportedImageExtensions.some(ext => thumbnailUrlValue.includes(ext));
        if (!hasValidImageExtension) {
            showAlert('缩略图URL应包含支持的图片格式扩展名（jpg、jpeg、png、gif、bmp、webp）', 'warning');
            return false;
        }
    }

    return true;
}

/**
 * 显示提示信息
 * @param {string} message - 提示消息
 * @param {string} type - 提示类型
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHtml);
    
    // 自动移除提示
    setTimeout(() => {
        const alertElement = document.getElementById(alertId);
        if (alertElement) {
            alertElement.remove();
        }
    }, 5000);
}

/**
 * 预览视频URL
 * @param {string} videoUrl - 视频URL
 */
function previewVideoUrl(videoUrl) {
    if (!videoUrl) return;

    const previewContainer = document.getElementById('videoPreview');
    if (!previewContainer) return;

    try {
        // 验证URL
        new URL(videoUrl);

        // 创建视频预览元素
        const video = document.createElement('video');
        video.src = videoUrl;
        video.controls = true;
        video.style.width = '100%';
        video.style.maxHeight = '300px';
        video.className = 'mt-3 rounded';
        video.preload = 'metadata';

        // 清空之前的预览
        previewContainer.innerHTML = '';
        previewContainer.appendChild(video);

        // 显示URL信息
        const urlInfo = document.createElement('div');
        urlInfo.className = 'mt-2 text-muted small';
        urlInfo.innerHTML = `
            <i class="fas fa-info-circle me-1"></i>
            视频URL: <a href="${videoUrl}" target="_blank" class="text-decoration-none">${videoUrl}</a>
        `;
        previewContainer.appendChild(urlInfo);

        // 监听视频加载错误
        video.addEventListener('error', function() {
            previewContainer.innerHTML = `
                <div class="alert alert-warning mt-3" role="alert">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    无法加载视频预览，请检查URL是否正确
                </div>
            `;
        });

    } catch (error) {
        previewContainer.innerHTML = `
            <div class="alert alert-danger mt-3" role="alert">
                <i class="fas fa-times-circle me-1"></i>
                无效的视频URL格式
            </div>
        `;
    }
}

/**
 * 重置表单
 */
function resetForm() {
    const form = document.getElementById('addVideoForm');
    if (form) {
        form.reset();

        // 清空预览
        const previewContainer = document.getElementById('videoPreview');
        if (previewContainer) {
            previewContainer.innerHTML = '';
        }

        // 隐藏进度条
        const progressContainer = document.getElementById('uploadProgress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
    }
}

/**
 * 添加URL输入事件监听器
 */
function addUrlInputListeners() {
    const videoUrlInput = document.getElementById('videoUrl');
    const thumbnailUrlInput = document.getElementById('thumbnailUrl');

    if (videoUrlInput) {
        videoUrlInput.addEventListener('blur', function() {
            const url = this.value.trim();
            if (url) {
                previewVideoUrl(url);
            }
        });
    }

    if (thumbnailUrlInput) {
        thumbnailUrlInput.addEventListener('blur', function() {
            const url = this.value.trim();
            if (url) {
                previewThumbnailUrl(url);
            }
        });
    }
}

/**
 * 预览缩略图URL
 * @param {string} thumbnailUrl - 缩略图URL
 */
function previewThumbnailUrl(thumbnailUrl) {
    if (!thumbnailUrl) return;

    const previewContainer = document.getElementById('thumbnailPreview');
    if (!previewContainer) return;

    try {
        // 验证URL
        new URL(thumbnailUrl);

        // 创建图片预览元素
        const img = document.createElement('img');
        img.src = thumbnailUrl;
        img.style.width = '100%';
        img.style.maxHeight = '200px';
        img.style.objectFit = 'cover';
        img.className = 'mt-3 rounded';

        // 清空之前的预览
        previewContainer.innerHTML = '';
        previewContainer.appendChild(img);

        // 监听图片加载错误
        img.addEventListener('error', function() {
            previewContainer.innerHTML = `
                <div class="alert alert-warning mt-3" role="alert">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    无法加载缩略图预览，请检查URL是否正确
                </div>
            `;
        });

    } catch (error) {
        previewContainer.innerHTML = `
            <div class="alert alert-danger mt-3" role="alert">
                <i class="fas fa-times-circle me-1"></i>
                无效的缩略图URL格式
            </div>
        `;
    }
}

// 导出到全局作用域
window.AddVideoPage = {
    handleVideoUpload,
    validateForm,
    showAlert,
    previewVideoUrl,
    previewThumbnailUrl,
    resetForm,
    addUrlInputListeners
};
