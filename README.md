# 极简版视频播放器

一个高度优化的Spring Boot视频播放器项目，专注于核心功能，删除所有冗余代码。

## 🎯 项目特点

- **极简架构**: 删除了所有未使用的依赖、字段和方法
- **核心功能**: 专注于视频的播放、管理和基本CRUD操作
- **高度优化**: 移除了Security、DevTools、H2等非必需组件
- **轻量级**: 最精简的代码结构和配置

## 🏗️ 技术栈

- **后端**: Spring Boot 3.2.0 + Java 17
- **数据库**: MySQL 8.0 + JPA
- **前端**: Thymeleaf + Bootstrap 5
- **容器化**: Docker + Docker Compose
- **依赖**: 仅保留核心必需依赖

## 📁 项目结构

```
src/
├── main/
│   ├── java/com/videoplayer/
│   │   ├── VideoPlayerApplication.java     # 主启动类
│   │   ├── controller/
│   │   │   ├── VideoApi.java              # 视频API控制器
│   │   │   └── PageController.java        # 页面控制器
│   │   ├── entity/
│   │   │   └── Video.java                 # 视频实体
│   │   ├── repository/
│   │   │   └── VideoRepository.java       # 视频数据访问
│   │   ├── service/
│   │   │   └── VideoService.java          # 视频业务逻辑
│   │   ├── config/
│   │   │   └── WebConfig.java             # Web配置
│   │   ├── common/
│   │   │   └── ApiResponse.java           # 统一响应格式
│   │   └── exception/
│   │       ├── GlobalExceptionHandler.java
│   │       ├── BusinessException.java
│   │       └── ResourceNotFoundException.java
│   ├── resources/
│   │   ├── application.yml                # 应用配置
│   │   ├── templates/                     # 页面模板
│   │   │   ├── index.html                # 首页
│   │   │   ├── videos.html               # 视频列表
│   │   │   ├── play.html                 # 播放页面
│   │   │   ├── admin.html                # 管理页面
│   │   │   └── error.html                # 错误页面
│   │   └── static/                       # 静态资源
│   │       ├── css/                      # 样式文件
│   │       ├── js/                       # JavaScript文件
│   │       └── images/                   # 图片资源
└── test/                                 # 测试代码
```

## 🚀 核心功能

### 视频管理
- ✅ 视频上传和存储（阿里云OSS）
- ✅ 缩略图上传和管理
- ✅ 视频信息编辑
- ✅ 视频删除（软删除/物理删除）
- ✅ 视频列表展示
- ✅ 视频搜索功能
- ✅ 视频置顶到首页功能

### 视频播放
- ✅ 基于OSS的视频播放
- ✅ 支持多种视频格式（MP4、AVI、MOV等）
- ✅ 响应式播放器界面
- ✅ 自动缩略图显示

### 文件存储
- ✅ 阿里云OSS集成
- ✅ 自动文件上传
- ✅ CDN加速访问
- ✅ 文件删除管理

### 管理功能
- ✅ 可视化管理后台
- ✅ 文件上传界面
- ✅ 视频状态管理
- ✅ 批量操作支持
- ✅ 视频置顶管理

## 📊 API接口

### 视频相关API
- `GET /api/videos` - 获取视频列表（分页）
- `GET /api/videos/{id}` - 获取视频详情
- `GET /api/videos/search` - 搜索视频
- `POST /api/videos` - 添加视频
- `PUT /api/videos/{id}` - 更新视频
- `DELETE /api/videos/{id}` - 删除视频
- `PUT /api/videos/{id}/pin` - 切换视频置顶状态
- `PUT /api/videos/{id}/pin/{status}` - 设置视频置顶状态

### 文件上传API
- `POST /api/upload/video` - 上传视频文件
- `POST /api/upload/thumbnail` - 上传缩略图
- `POST /api/upload/video-with-thumbnail` - 一次性上传视频和缩略图

### 页面路由
- `/` - 首页
- `/videos` - 视频列表页
- `/play/{id}` - 视频播放页
- `/admin` - 管理页面
- `/admin/add` - 添加视频页面
- `/admin/edit/{id}` - 编辑视频页面

## 🐳 快速部署

### 使用Docker Compose
```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 本地开发

#### 1. 配置阿里云OSS
```bash
# 复制配置文件
cp .env.example .env

# 编辑.env文件，填入您的OSS配置信息
# 详细配置说明请参考 OSS_SETUP.md
```

#### 2. 启动数据库
确保MySQL数据库已启动，并执行初始化脚本：
```bash
mysql -u root -p < database/init.sql
```

#### 3. 启动应用
```bash
# 使用启动脚本（推荐）
./start.sh        # Linux/Mac
start.bat         # Windows

# 或直接使用Maven
mvn spring-boot:run
```

## 🔧 配置说明

### 阿里云OSS配置
1. 复制 `.env.example` 为 `.env`
2. 填入您的阿里云OSS配置信息：

```bash
# 阿里云OSS配置
OSS_ENDPOINT=https://oss-cn-guangzhou.aliyuncs.com
OSS_ACCESS_KEY_ID=your-access-key-id
OSS_ACCESS_KEY_SECRET=your-access-key-secret
OSS_BUCKET_NAME=your-bucket-name
OSS_VIDEO_DIR=video-player/videos
OSS_THUMBNAIL_DIR=video-player/thumbnails
OSS_BASE_URL=https://your-bucket-name.oss-cn-guangzhou.aliyuncs.com
```

### 数据库配置
```yaml
spring:
  datasource:
    url: ****************************************
    username: root
    password: root
```

### 文件上传配置
```yaml
spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
```

## 📝 数据库表结构

### videos表
- `id` - 主键
- `title` - 视频标题
- `description` - 视频描述
- `video_url` - 视频URL
- `thumbnail_url` - 缩略图URL
- `file_size` - 文件大小
- `video_format` - 视频格式
- `resolution` - 分辨率
- `is_active` - 是否启用
- `is_pinned` - 是否置顶到首页
- `created_time` - 创建时间
- `updated_time` - 更新时间

## 🎉 优化说明

此极简版删除了以下冗余内容：

### 删除的依赖项
- ❌ Spring Security（无认证需求）
- ❌ H2数据库（仅使用MySQL）
- ❌ Spring Boot DevTools（生产环境不需要）

### 删除的实体字段
- ❌ playCount, likeCount（统计功能）
- ❌ categoryId, tags（分类功能）
- ❌ status（状态管理）
- ❌ createdBy, updatedBy（审计字段）

### 删除的方法和类
- ❌ 未使用的Service方法
- ❌ 冗余的Repository查询
- ❌ 复杂的异常处理器
- ❌ 防夜间模式相关代码

### 删除的配置文件
- ❌ 多个数据库迁移脚本
- ❌ 测试配置文件
- ❌ 复杂的Docker健康检查

## 📞 支持

如需帮助，请查看代码注释或提交Issue。

---

**专注核心，简单高效！**
