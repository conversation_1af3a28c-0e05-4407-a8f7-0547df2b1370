-- 数据库迁移脚本：添加视频置顶功能
-- 执行时间：2025年
-- 说明：为videos表添加is_pinned字段，支持视频置顶到首页功能

USE `video_player`;

-- 检查字段是否已存在，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'video_player'
    AND TABLE_NAME = 'videos'
    AND COLUMN_NAME = 'is_pinned'
);

-- 如果字段不存在，则添加字段
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `videos` ADD COLUMN `is_pinned` BOOLEAN DEFAULT FALSE COMMENT "是否置顶到首页" AFTER `is_active`',
    'SELECT "字段 is_pinned 已存在，跳过添加" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果不存在）
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = 'video_player'
    AND TABLE_NAME = 'videos'
    AND INDEX_NAME = 'idx_is_pinned'
);

SET @sql = IF(@index_exists = 0,
    'ALTER TABLE `videos` ADD INDEX `idx_is_pinned` (`is_pinned`)',
    'SELECT "索引 idx_is_pinned 已存在，跳过添加" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示表结构确认
DESCRIBE `videos`;

-- 显示完成信息
SELECT '数据库迁移完成！已添加视频置顶功能字段。' as message;
