<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light only">
    <meta name="theme-color" content="#ffffff">
    <meta name="msapplication-navbutton-color" content="#ffffff">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="supported-color-schemes" content="light">
    <!-- 强制防夜间模式 -->
    <meta name="prefers-color-scheme" content="light">
    <meta name="force-color-scheme" content="light">
    <style>
        :root { color-scheme: light only !important; -webkit-color-scheme: light only !important; }
        html, body { background-color: #ffffff !important; color: #333333 !important; color-scheme: light only !important; -webkit-color-scheme: light only !important; }
    </style>
    <title th:text="${pageTitle}">佳茵轻康 - 编辑视频</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/admin-style.css" rel="stylesheet">



</head>
<body style="background-color: #ffffff !important; color: #333333 !important; color-scheme: light only !important;">
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top" style="background-color: #0d6efd !important; color-scheme: light only !important;">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container my-4">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <!-- 页面标题 -->
                <div class="d-flex align-items-center mb-4">
                    <a href="/admin" class="btn btn-outline-secondary me-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-edit me-2 text-primary"></i>编辑视频
                    </h1>
                </div>

                <!-- 编辑视频表单 -->
                <div class="card shadow-sm">
                    <div class="card-body">
                        <form id="editVideoForm">
                            <input type="hidden" id="videoId" th:value="${video.id}">
                            
                            <!-- 当前视频预览 -->
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-eye me-1"></i>当前视频
                                </label>
                                <div class="card">
                                    <div class="row g-0">
                                        <div class="col-md-4">
                                            <img th:src="${video.thumbnailUrl != null ? video.thumbnailUrl : 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png'}"
                                                 class="img-fluid rounded-start" 
                                                 th:alt="${video.title}"
                                                 style="height: 120px; object-fit: cover; width: 100%;">
                                        </div>
                                        <div class="col-md-8">
                                            <div class="card-body">
                                                <h6 class="card-title" th:text="${video.title}">视频标题</h6>
                                                <p class="card-text">
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        <span th:text="${#temporals.format(video.createdTime, 'yyyy-MM-dd HH:mm')}">2024-01-01 12:00</span>
                                                    </small>
                                                </p>
                                                <a th:href="@{/play/{id}(id=${video.id})}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                    <i class="fas fa-play me-1"></i>预览
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 视频标题 -->
                            <div class="mb-3">
                                <label for="title" class="form-label">
                                    <i class="fas fa-heading me-1"></i>视频标题 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       th:value="${video.title}" placeholder="请输入视频标题" maxlength="200" required>
                                <div class="invalid-feedback"></div>
                            </div>

                            <!-- 视频描述 -->
                            <div class="mb-3">
                                <label for="description" class="form-label">
                                    <i class="fas fa-align-left me-1"></i>视频描述
                                </label>
                                <textarea class="form-control" id="description" name="description" 
                                          rows="3" placeholder="请输入视频描述" maxlength="500" th:text="${video.description}"></textarea>
                                <div class="invalid-feedback"></div>
                            </div>

                            <!-- 视频URL -->
                            <div class="mb-3">
                                <label for="videoUrl" class="form-label">
                                    <i class="fas fa-link me-1"></i>视频URL <span class="text-danger">*</span>
                                </label>
                                <input type="url" class="form-control" id="videoUrl" name="videoUrl" 
                                       th:value="${video.videoUrl}" placeholder="请输入视频URL" required>
                                <div class="invalid-feedback"></div>
                            </div>

                            <!-- 缩略图URL -->
                            <div class="mb-3">
                                <label for="thumbnailUrl" class="form-label">
                                    <i class="fas fa-image me-1"></i>缩略图URL
                                </label>
                                <input type="url" class="form-control" id="thumbnailUrl" name="thumbnailUrl" 
                                       th:value="${video.thumbnailUrl}" placeholder="请输入缩略图URL">
                                <div class="invalid-feedback"></div>
                            </div>

                            <!-- 视频参数 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="videoFormat" class="form-label">
                                        <i class="fas fa-file-video me-1"></i>格式
                                    </label>
                                    <select class="form-select" id="videoFormat" name="videoFormat">
                                        <option value="">请选择</option>
                                        <option value="mp4" th:selected="${video.videoFormat == 'mp4'}">MP4</option>
                                        <option value="avi" th:selected="${video.videoFormat == 'avi'}">AVI</option>
                                        <option value="mov" th:selected="${video.videoFormat == 'mov'}">MOV</option>
                                        <option value="wmv" th:selected="${video.videoFormat == 'wmv'}">WMV</option>
                                        <option value="flv" th:selected="${video.videoFormat == 'flv'}">FLV</option>
                                        <option value="webm" th:selected="${video.videoFormat == 'webm'}">WebM</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="resolution" class="form-label">
                                        <i class="fas fa-expand me-1"></i>分辨率
                                    </label>
                                    <select class="form-select" id="resolution" name="resolution">
                                        <option value="">请选择</option>
                                        <option value="4K" th:selected="${video.resolution == '4K'}">4K (3840×2160)</option>
                                        <option value="1080p" th:selected="${video.resolution == '1080p'}">1080p (1920×1080)</option>
                                        <option value="720p" th:selected="${video.resolution == '720p'}">720p (1280×720)</option>
                                        <option value="480p" th:selected="${video.resolution == '480p'}">480p (854×480)</option>
                                        <option value="360p" th:selected="${video.resolution == '360p'}">360p (640×360)</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="/admin" class="btn btn-outline-secondary me-md-2">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>保存更改
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 全局提示容器 -->
    <div id="alertContainer" class="position-fixed top-0 start-50 translate-middle-x" style="z-index: 9999; margin-top: 20px;"></div>

    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div>正在处理，请稍候...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义JS -->
    <script src="/js/admin.js"></script>
    <script src="/js/edit-video.js"></script>
</body>
</html>
