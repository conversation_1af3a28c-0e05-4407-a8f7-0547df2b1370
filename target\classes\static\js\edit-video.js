/**
 * 编辑视频页面专用JavaScript功能
 * <AUTHOR>
 * @version 1.0.0
 */

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeEditVideoPage();
});

/**
 * 初始化编辑视频页面
 */
function initializeEditVideoPage() {
    const form = document.getElementById('editVideoForm');
    if (form) {
        form.addEventListener('submit', submitEditVideo);
    }
    console.log('编辑视频页面初始化完成');
}

/**
 * 提交编辑视频表单
 * @param {Event} event - 表单提交事件
 */
async function submitEditVideo(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const videoId = form.querySelector('#videoId').value;
    
    try {
        // 验证表单
        if (!validateEditForm(form)) {
            return;
        }
        
        // 显示加载状态
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';
        
        // 构建更新数据
        const updateData = {
            title: formData.get('title'),
            description: formData.get('description'),
            videoUrl: formData.get('videoUrl'),
            thumbnailUrl: formData.get('thumbnailUrl'),
            fileSize: formData.get('fileSize') ? parseInt(formData.get('fileSize')) : null,
            videoFormat: formData.get('videoFormat'),
            resolution: formData.get('resolution')
        };
        
        // 发送更新请求
        const response = await fetch(`/api/videos/${videoId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(updateData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('视频信息更新成功！', 'success');
            setTimeout(() => {
                window.location.href = '/admin';
            }, 1500);
        } else {
            throw new Error(result.message || '更新失败');
        }
        
    } catch (error) {
        console.error('更新失败:', error);
        showAlert('更新失败: ' + error.message, 'danger');
        
        // 重置按钮状态
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>保存更改';
    }
}

/**
 * 验证编辑表单
 * @param {HTMLFormElement} form - 表单元素
 * @returns {boolean} 验证结果
 */
function validateEditForm(form) {
    const title = form.querySelector('#title');
    const videoUrl = form.querySelector('#videoUrl');
    
    if (!title.value.trim()) {
        showAlert('请输入视频标题', 'warning');
        title.focus();
        return false;
    }
    
    if (!videoUrl.value.trim()) {
        showAlert('请输入视频URL', 'warning');
        videoUrl.focus();
        return false;
    }
    
    // 验证URL格式
    try {
        new URL(videoUrl.value);
    } catch {
        showAlert('请输入有效的视频URL', 'warning');
        videoUrl.focus();
        return false;
    }
    
    // 验证标题长度
    if (title.value.length > 200) {
        showAlert('视频标题长度不能超过200个字符', 'warning');
        title.focus();
        return false;
    }
    
    // 验证描述长度
    const description = form.querySelector('#description');
    if (description && description.value.length > 500) {
        showAlert('视频描述长度不能超过500个字符', 'warning');
        description.focus();
        return false;
    }
    
    return true;
}

/**
 * 显示提示信息
 * @param {string} message - 提示消息
 * @param {string} type - 提示类型
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHtml);
    
    // 自动移除提示
    setTimeout(() => {
        const alertElement = document.getElementById(alertId);
        if (alertElement) {
            alertElement.remove();
        }
    }, 5000);
}

/**
 * 预览视频URL
 */
function previewVideoUrl() {
    const videoUrlInput = document.getElementById('videoUrl');
    const previewContainer = document.getElementById('videoPreview');
    
    if (!videoUrlInput || !previewContainer) return;
    
    const videoUrl = videoUrlInput.value.trim();
    if (!videoUrl) {
        previewContainer.innerHTML = '';
        return;
    }
    
    try {
        // 验证URL
        new URL(videoUrl);
        
        // 创建视频预览
        const video = document.createElement('video');
        video.src = videoUrl;
        video.controls = true;
        video.style.width = '100%';
        video.style.maxHeight = '300px';
        video.className = 'mt-3 rounded';
        
        // 清空之前的预览
        previewContainer.innerHTML = '';
        previewContainer.appendChild(video);
        
        // 添加预览标题
        const previewTitle = document.createElement('div');
        previewTitle.className = 'mt-2 text-muted small';
        previewTitle.innerHTML = '<i class="fas fa-eye me-1"></i>视频预览';
        previewContainer.insertBefore(previewTitle, video);
        
    } catch (error) {
        previewContainer.innerHTML = '<div class="alert alert-warning mt-3"><i class="fas fa-exclamation-triangle me-2"></i>无效的视频URL</div>';
    }
}

/**
 * 预览缩略图URL
 */
function previewThumbnailUrl() {
    const thumbnailUrlInput = document.getElementById('thumbnailUrl');
    const previewContainer = document.getElementById('thumbnailPreview');
    
    if (!thumbnailUrlInput || !previewContainer) return;
    
    const thumbnailUrl = thumbnailUrlInput.value.trim();
    if (!thumbnailUrl) {
        previewContainer.innerHTML = '';
        return;
    }
    
    try {
        // 验证URL
        new URL(thumbnailUrl);
        
        // 创建图片预览
        const img = document.createElement('img');
        img.src = thumbnailUrl;
        img.style.width = '100%';
        img.style.maxHeight = '200px';
        img.style.objectFit = 'cover';
        img.className = 'mt-3 rounded';
        
        // 错误处理
        img.onerror = function() {
            previewContainer.innerHTML = '<div class="alert alert-warning mt-3"><i class="fas fa-exclamation-triangle me-2"></i>无法加载缩略图</div>';
        };
        
        // 清空之前的预览
        previewContainer.innerHTML = '';
        previewContainer.appendChild(img);
        
        // 添加预览标题
        const previewTitle = document.createElement('div');
        previewTitle.className = 'mt-2 text-muted small';
        previewTitle.innerHTML = '<i class="fas fa-image me-1"></i>缩略图预览';
        previewContainer.insertBefore(previewTitle, img);
        
    } catch (error) {
        previewContainer.innerHTML = '<div class="alert alert-warning mt-3"><i class="fas fa-exclamation-triangle me-2"></i>无效的缩略图URL</div>';
    }
}

/**
 * 重置表单到原始状态
 */
function resetForm() {
    const form = document.getElementById('editVideoForm');
    if (form) {
        // 重置表单字段
        form.reset();
        
        // 清空预览
        const videoPreview = document.getElementById('videoPreview');
        const thumbnailPreview = document.getElementById('thumbnailPreview');
        
        if (videoPreview) videoPreview.innerHTML = '';
        if (thumbnailPreview) thumbnailPreview.innerHTML = '';
        
        showAlert('表单已重置', 'info');
    }
}

/**
 * 删除视频
 * @param {string} videoId - 视频ID
 */
async function deleteVideo(videoId) {
    if (!confirm('确定要删除这个视频吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/videos/${videoId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('视频删除成功！', 'success');
            setTimeout(() => {
                window.location.href = '/admin';
            }, 1500);
        } else {
            throw new Error(result.message || '删除失败');
        }
        
    } catch (error) {
        console.error('删除失败:', error);
        showAlert('删除失败: ' + error.message, 'danger');
    }
}

// 导出到全局作用域
window.EditVideoPage = {
    submitEditVideo,
    validateEditForm,
    showAlert,
    previewVideoUrl,
    previewThumbnailUrl,
    resetForm,
    deleteVideo
};
