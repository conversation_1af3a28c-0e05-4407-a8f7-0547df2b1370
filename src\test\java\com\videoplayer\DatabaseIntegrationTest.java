package com.videoplayer;

import com.videoplayer.entity.Video;
import com.videoplayer.repository.VideoRepository;
import com.videoplayer.service.VideoService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据库集成测试
 * 只有在数据库连接正常时才运行
 */
@SpringBootTest
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.datasource.username=root",
    "spring.datasource.password=root"
})
@Transactional  // 测试后回滚数据
public class DatabaseIntegrationTest {

    @Autowired
    private VideoRepository videoRepository;

    @Autowired
    private VideoService videoService;

    @Test
    public void testDatabaseConnection() {
        // 测试数据库连接是否正常
        assertNotNull(videoRepository);
        assertNotNull(videoService);
        
        // 测试基本查询
        long count = videoRepository.count();
        assertTrue(count >= 0);
    }

    @Test
    public void testVideoEntityOperations() {
        // 创建测试视频
        Video video = new Video();
        video.setTitle("测试视频");
        video.setDescription("这是一个测试视频");
        video.setVideoUrl("https://test.com/video.mp4");
        video.setThumbnailUrl("https://test.com/thumbnail.jpg");
        video.setFileSize(1024L);
        video.setVideoFormat("mp4");
        video.setResolution("1080p");

        // 保存视频
        Video savedVideo = videoRepository.save(video);
        assertNotNull(savedVideo.getId());
        assertEquals("测试视频", savedVideo.getTitle());

        // 查询视频
        Video foundVideo = videoRepository.findById(savedVideo.getId()).orElse(null);
        assertNotNull(foundVideo);
        assertEquals("测试视频", foundVideo.getTitle());

        // 删除视频
        videoRepository.delete(savedVideo);
        assertFalse(videoRepository.findById(savedVideo.getId()).isPresent());
    }

    @Test
    public void testVideoService() {
        // 测试VideoService的基本功能
        assertNotNull(videoService);

        // 测试获取视频列表（分页）
        var videos = videoService.getAllVideos(0, 10);
        assertNotNull(videos);

        // 测试获取管理员视频列表
        var adminVideos = videoService.getAllVideosForAdmin();
        assertNotNull(adminVideos);

        // 测试分页查询
        var pagedVideos = videoService.getAllActiveVideos(0, 10, "createdTime", "desc");
        assertNotNull(pagedVideos);
    }
}
