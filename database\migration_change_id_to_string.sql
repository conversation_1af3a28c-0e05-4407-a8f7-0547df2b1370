-- 数据库迁移脚本：将视频ID从BIGINT改为VARCHAR(18)
-- 注意：此脚本会重新生成所有视频的ID，请在执行前备份数据库！

USE `video_player`;

-- 1. 创建临时表存储现有数据
CREATE TEMPORARY TABLE temp_videos AS 
SELECT 
    title, description, video_url, thumbnail_url, 
    file_size, video_format, resolution, 
    is_active, is_pinned, created_time, updated_time
FROM videos;

-- 2. 删除原表
DROP TABLE videos;

-- 3. 重新创建表，使用VARCHAR(18)作为主键
CREATE TABLE `videos` (
    `id` VARCHAR(18) PRIMARY KEY COMMENT '视频ID（18位随机字符串）',
    `title` VARCHAR(200) NOT NULL COMMENT '视频标题',
    `description` VARCHAR(500) DEFAULT NULL COMMENT '视频描述',
    `video_url` VARCHAR(1000) NOT NULL COMMENT '视频URL地址',
    `thumbnail_url` VARCHAR(1000) DEFAULT NULL COMMENT '缩略图URL',
    `file_size` BIGINT DEFAULT NULL COMMENT '文件大小（字节）',
    `video_format` VARCHAR(20) DEFAULT NULL COMMENT '视频格式',
    `resolution` VARCHAR(20) DEFAULT NULL COMMENT '分辨率',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `is_pinned` BOOLEAN DEFAULT FALSE COMMENT '是否置顶到首页',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_title` (`title`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_is_pinned` (`is_pinned`),
    INDEX `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频信息表';

-- 4. 生成随机ID的函数（MySQL 8.0+）
DELIMITER $$
CREATE FUNCTION generate_random_id() RETURNS VARCHAR(18)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE chars VARCHAR(62) DEFAULT 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    DECLARE result VARCHAR(18) DEFAULT '';
    DECLARE i INT DEFAULT 0;
    
    WHILE i < 18 DO
        SET result = CONCAT(result, SUBSTRING(chars, FLOOR(1 + RAND() * 62), 1));
        SET i = i + 1;
    END WHILE;
    
    RETURN result;
END$$
DELIMITER ;

-- 5. 将临时表数据插入新表（使用随机生成的ID）
INSERT INTO videos (id, title, description, video_url, thumbnail_url, file_size, video_format, resolution, is_active, is_pinned, created_time, updated_time)
SELECT 
    generate_random_id() as id,
    title, description, video_url, thumbnail_url, 
    file_size, video_format, resolution, 
    is_active, is_pinned, created_time, updated_time
FROM temp_videos;

-- 6. 删除临时函数
DROP FUNCTION generate_random_id;

-- 7. 显示迁移结果
SELECT COUNT(*) as migrated_videos FROM videos;
SELECT '数据库ID字段迁移完成！新的视频ID已生成。' as message;

-- 注意事项：
-- 1. 执行此脚本前请务必备份数据库
-- 2. 此脚本会重新生成所有视频的ID
-- 3. 如果有其他表引用了videos表的ID，需要同步更新
-- 4. 建议在测试环境先验证脚本的正确性
