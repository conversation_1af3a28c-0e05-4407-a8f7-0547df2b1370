<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light only">
    <meta name="theme-color" content="#ffffff">
    <meta name="msapplication-navbutton-color" content="#ffffff">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="supported-color-schemes" content="light">
    <!-- 强制防夜间模式 -->
    <meta name="prefers-color-scheme" content="light">
    <meta name="force-color-scheme" content="light">
    <style>
        :root { color-scheme: light only !important; -webkit-color-scheme: light only !important; }
        html, body { background-color: #ffffff !important; color: #333333 !important; color-scheme: light only !important; -webkit-color-scheme: light only !important; }
    </style>
    <title>佳茵轻康 - 出错了</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">



</head>
<body style="background-color: #ffffff !important; color: #333333 !important; color-scheme: light only !important;">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top" style="background-color: #0d6efd !important; color-scheme: light only !important;">
        <div class="container">
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/videos">
                            <i class="fas fa-video me-1"></i>所有视频
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin">
                            <i class="fas fa-cog me-1"></i>管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 错误内容 -->
    <main class="container my-5">
        <div class="error-container">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <h1 class="display-4 fw-bold text-danger mb-4">出错了！</h1>
            
            <div class="alert alert-danger" role="alert" th:if="${error}">
                <i class="fas fa-info-circle me-2"></i>
                <span th:text="${error}">发生了未知错误</span>
            </div>
            
            <div class="alert alert-warning" role="alert" th:unless="${error}">
                <i class="fas fa-info-circle me-2"></i>
                页面加载时发生了错误，请稍后重试。
            </div>
            
            <div class="mt-4">
                <a href="/" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-home me-2"></i>返回首页
                </a>
                <button onclick="history.back()" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>返回上页
                </button>
            </div>
            
            <div class="mt-5">
                <h5>可能的解决方案：</h5>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>检查网络连接是否正常</li>
                    <li><i class="fas fa-check text-success me-2"></i>确认视频链接是否有效</li>
                    <li><i class="fas fa-check text-success me-2"></i>刷新页面重新尝试</li>
                    <li><i class="fas fa-check text-success me-2"></i>联系管理员获取帮助</li>
                </ul>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="col-md-6">
            <!-- <h5><i class="fas fa-play-circle me-2"></i>佳茵轻康</h5> -->
                <p class="mb-0">轻康自然，享瘦生活。</p>
            </div>
            <div class="col-md-6 text-md-end">
                <p class="mb-0">
                    <small>© 2025 加盟合作. <a href="/about" class="text-light text-decoration-none">点这里：联系我们</a></small>
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/main.js"></script>
</body>
</html>

