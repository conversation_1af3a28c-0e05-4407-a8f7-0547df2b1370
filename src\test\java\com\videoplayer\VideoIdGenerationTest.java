package com.videoplayer;

import com.videoplayer.entity.Video;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 视频ID生成测试（不依赖Spring上下文）
 */
public class VideoIdGenerationTest {

    // 18位字符串，包含大小写字母和数字的正则表达式
    private static final Pattern ID_PATTERN = Pattern.compile("^[A-Za-z0-9]{18}$");

    @Test
    public void testVideoIdGeneration() {
        // 创建视频实例
        Video video = new Video();
        
        // 验证ID不为空
        assertNotNull(video.getId(), "视频ID不应为空");
        
        // 验证ID长度为18位
        assertEquals(18, video.getId().length(), "视频ID长度应为18位");
        
        // 验证ID只包含大小写字母和数字
        assertTrue(ID_PATTERN.matcher(video.getId()).matches(), 
                  "视频ID应只包含大小写字母和数字");
        
        System.out.println("生成的视频ID: " + video.getId());
    }

    @Test
    public void testVideoIdUniqueness() {
        // 生成多个视频ID，验证唯一性
        Set<String> generatedIds = new HashSet<>();
        int testCount = 1000;
        
        for (int i = 0; i < testCount; i++) {
            Video video = new Video();
            String id = video.getId();
            
            // 验证ID格式
            assertTrue(ID_PATTERN.matcher(id).matches(), 
                      "视频ID应只包含大小写字母和数字");
            
            // 验证唯一性
            assertFalse(generatedIds.contains(id), 
                       "生成的ID应该是唯一的，但发现重复ID: " + id);
            
            generatedIds.add(id);
        }
        
        assertEquals(testCount, generatedIds.size(), 
                    "应该生成" + testCount + "个唯一的ID");
        
        System.out.println("成功生成 " + testCount + " 个唯一的视频ID");
        System.out.println("示例ID: " + generatedIds.iterator().next());
    }

    @Test
    public void testVideoWithTitleAndUrl() {
        // 测试带参数的构造函数
        String title = "测试视频";
        String videoUrl = "https://example.com/video.mp4";
        
        Video video = new Video(title, videoUrl);
        
        // 验证ID生成
        assertNotNull(video.getId(), "视频ID不应为空");
        assertEquals(18, video.getId().length(), "视频ID长度应为18位");
        assertTrue(ID_PATTERN.matcher(video.getId()).matches(), 
                  "视频ID应只包含大小写字母和数字");
        
        // 验证其他属性
        assertEquals(title, video.getTitle(), "视频标题应正确设置");
        assertEquals(videoUrl, video.getVideoUrl(), "视频URL应正确设置");
        assertNotNull(video.getCreatedTime(), "创建时间应自动设置");
        assertNotNull(video.getUpdatedTime(), "更新时间应自动设置");
        
        System.out.println("带参数构造的视频ID: " + video.getId());
    }

    @Test
    public void testIdCharacterDistribution() {
        // 测试ID中字符的分布情况
        Set<Character> usedChars = new HashSet<>();
        int testCount = 100;
        
        for (int i = 0; i < testCount; i++) {
            Video video = new Video();
            String id = video.getId();
            
            for (char c : id.toCharArray()) {
                usedChars.add(c);
            }
        }
        
        // 验证使用了大写字母
        boolean hasUpperCase = usedChars.stream().anyMatch(Character::isUpperCase);
        assertTrue(hasUpperCase, "应该包含大写字母");
        
        // 验证使用了小写字母
        boolean hasLowerCase = usedChars.stream().anyMatch(Character::isLowerCase);
        assertTrue(hasLowerCase, "应该包含小写字母");
        
        // 验证使用了数字
        boolean hasDigit = usedChars.stream().anyMatch(Character::isDigit);
        assertTrue(hasDigit, "应该包含数字");
        
        System.out.println("在 " + testCount + " 个ID中使用了 " + usedChars.size() + " 种不同字符");
        System.out.println("使用的字符示例: " + usedChars.toString().substring(0, Math.min(50, usedChars.toString().length())));
    }
}
