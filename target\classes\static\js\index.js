/**
 * 首页专用JavaScript功能
 * <AUTHOR>
 * @version 1.0.0
 */

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeIndexPage();
});

/**
 * 初始化首页
 */
function initializeIndexPage() {
    console.log('首页初始化完成');
}

/**
 * 联系信息位置调整函数
 * @param {string} top - 顶部边距
 * @param {string} bottom - 底部边距  
 * @param {string} height - 高度
 */
function adjustContact(top, bottom, height) {
    const root = document.documentElement;
    if (top !== undefined) root.style.setProperty('--contact-margin-top', top);
    if (bottom !== undefined) root.style.setProperty('--contact-margin-bottom', bottom);
    if (height !== undefined) root.style.setProperty('--contact-height', height);
}

// 导出到全局作用域
window.IndexPage = {
    adjustContact
};
